export default {
  name: 'product',
  title: '<PERSON><PERSON><PERSON><PERSON>',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'string',
      validation: Rule => Rule.required().min(3).max(100)
    },
    {
      name: 'description',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'text',
      validation: Rule => Rule.required().min(10)
    },
    {
      name: 'color',
      title: '<PERSON><PERSON> Seçenekleri',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'image',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'image',
      options: {
        hotspot: true
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'category',
      title: '<PERSON><PERSON><PERSON>',
      type: 'reference',
      to: [{ type: 'category' }],
      validation: Rule => Rule.required()
    }
  ]
}
