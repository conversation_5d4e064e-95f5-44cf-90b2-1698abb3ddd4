import { defineType, defineField } from 'sanity'

export default defineType({
  name: 'product',
  title: '<PERSON><PERSON><PERSON><PERSON>',
  type: 'document',
  fields: [
    defineField({ name: 'title', title: '<PERSON><PERSON><PERSON><PERSON>', type: 'string' }),
    defineField({ name: 'description', title: '<PERSON><PERSON><PERSON><PERSON>', type: 'text' }),
    defineField({ name: 'color', title: '<PERSON><PERSON> Seçenekleri', type: 'string' }),
    defineField({ name: 'image', title: '<PERSON><PERSON><PERSON><PERSON>', type: 'image', options: { hotspot: true } }),
    defineField({ name: 'category', title: '<PERSON><PERSON><PERSON>', type: 'reference', to: [{ type: 'category' }] }),
  ],
})
