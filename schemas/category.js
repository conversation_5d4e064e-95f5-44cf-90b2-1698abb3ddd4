import { defineType, defineField } from 'sanity'

export default defineType({
  name: 'category',        // şema ismi (database’de koleksiyon adı gibi)
  title: 'Kategori',       // Studio’da gözükecek isim
  type: 'document',        // belge türü (collection gibi)

  fields: [
    defineField({
      name: 'title',       // alan adı
      title: '<PERSON><PERSON><PERSON> İsmi',  // Studio’da alan ba<PERSON>
      type: 'string',      // veri tipi
      validation: Rule => Rule.required().min(2),  // zorunlu, en az 2 karakter
    }),
    defineField({
      name: 'description',
      title: '<PERSON><PERSON>i Açıklaması',
      type: 'text',
      validation: Rule => Rule.optional(),
    }),
  ],
})
