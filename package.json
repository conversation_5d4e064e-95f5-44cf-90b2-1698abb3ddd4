{"name": "gazitech-products", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/desk-tool": "^2.36.2", "@sanity/vision": "^3.95.0", "react": "^18.3.1", "react-dom": "^18.3.1", "rxjs": "^7.8.2", "sanity": "^3.95.0", "styled-components": "^6.1.18"}, "devDependencies": {"@sanity/eslint-config-studio": "^5.0.2", "@types/react": "^19.1", "eslint": "^9.28", "prettier": "^3.5", "typescript": "^5.8"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}